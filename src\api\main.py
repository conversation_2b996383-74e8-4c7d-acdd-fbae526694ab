from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Request
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from ultralytics import <PERSON><PERSON><PERSON>
from PIL import Image
import os
import numpy as np
import tensorflow as tf
from io import BytesIO
import uvicorn
from tensorflow.keras.models import load_model, Sequential, Model
from tensorflow.keras.layers import RandomRotation, RandomFlip, Resizing, Rescaling, InputLayer, Input
import traceback

# Custom layers to handle compatibility issues
class CustomRandomRotation(RandomRotation):
    def __init__(self, factor, value_range=None, **kwargs):
        if value_range is not None:
            kwargs.pop('value_range', None)  # Remove value_range from kwargs
        super().__init__(factor=factor, **kwargs)

    @classmethod
    def from_config(cls, config):
        if 'value_range' in config:
            del config['value_range']
        return super().from_config(config)

class CustomRandomFlip(RandomFlip):
    def __init__(self, mode='horizontal_and_vertical', **kwargs):
        super().__init__(mode=mode, **kwargs)

# Custom Sequential model to handle nested models
class CustomSequential(Sequential):
    def __init__(self, layers=None, name=None):
        super().__init__(layers, name)

    @classmethod
    def from_config(cls, config, custom_objects=None):
        if custom_objects is None:
            custom_objects = {}
        return super().from_config(config, custom_objects=custom_objects)

# Initialize FastAPI app
app = FastAPI()

# Enable CORS for local development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,
)

# Add exception handlers to ensure CORS headers are sent with error responses
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers={"Access-Control-Allow-Origin": "http://localhost:5173"}
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content={"detail": str(exc)},
        headers={"Access-Control-Allow-Origin": "http://localhost:5173"}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    print(f"Unexpected error: {str(exc)}")
    print(f"Error type: {type(exc)}")
    print(f"Traceback: {traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={"detail": str(exc)},
        headers={"Access-Control-Allow-Origin": "http://localhost:5173"}
    )

# Directory to save annotated images
OUTPUT_DIR = "annotated_images"
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Load YOLO model
yolo_model = YOLO(os.path.join('src', 'api', 'Models', 'flatfoot_detection_yolov8.pt'))

# Load TensorFlow model with custom objects
custom_objects = {
    'RandomRotation': CustomRandomRotation,
    'RandomFlip': CustomRandomFlip,
    'Sequential': CustomSequential,
    'InputLayer': InputLayer,
    'Resizing': Resizing,
    'Rescaling': Rescaling
}

def create_model():
    """Create a new model with the same architecture"""
    inputs = Input(shape=(256, 256, 3))
    
    # Preprocessing layers
    x = Resizing(256, 256)(inputs)
    x = Rescaling(1./255)(x)
    
    # Convolutional layers
    x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    
    # Dense layers
    x = tf.keras.layers.Flatten()(x)
    x = tf.keras.layers.Dense(128, activation='relu')(x)
    outputs = tf.keras.layers.Dense(2, activation='softmax')(x)
    
    model = Model(inputs=inputs, outputs=outputs)
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    return model

try:
    print("Attempting to load model...")
    model_path = os.path.join('src', 'api', 'Models', 'hallux_valgus_model12.keras')
    print(f"Loading model from: {model_path}")
    tensorflow_model = tf.keras.models.load_model(
        model_path,
        custom_objects=custom_objects,
        compile=True
    )
    print("Model loaded successfully!")
except Exception as e:
    print(f"Error loading model: {str(e)}")
    print("Creating new model with same architecture...")
    tensorflow_model = create_model()
    print("New model created successfully!")

# Custom labels for YOLO
CUSTOM_LABELS = {0: "Flat Foot", 1: "Normal"}
CLASS_NAMES = ["Hallux Valgus", "Normal"]

@app.get("/ping")
async def ping():
    """
    Health check endpoint.
    """
    return {"message": "API is alive!"}

def read_file_as_image(data: bytes) -> np.ndarray:
    """
    Convert uploaded image file bytes to NumPy array.
    """
    try:
        print(f"Attempting to read image data of size: {len(data)} bytes")
        image = Image.open(BytesIO(data))
        print(f"Image format: {image.format}, Mode: {image.mode}, Size: {image.size}")
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            print(f"Converting image from {image.mode} to RGB")
            image = image.convert('RGB')
        
        # Convert to numpy array
        image_array = np.array(image)
        print(f"Successfully converted image to numpy array. Shape: {image_array.shape}, dtype: {image_array.dtype}")
        return image_array
    except Exception as e:
        print(f"Error converting image to numpy array: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=400, detail=f"Error processing image: {str(e)}")

# TensorFlow prediction endpoint
@app.post("/predict/tensorflow")
async def predict_tensorflow(file: UploadFile = File(...)):
    """
    Predict endpoint for TensorFlow model prediction.
    """
    try:
        print("Starting TensorFlow prediction...")
        print(f"Received file: {file.filename}, content_type: {file.content_type}")
        
        # Read the uploaded image
        print("Reading uploaded file...")
        file_content = await file.read()
        print(f"File content size: {len(file_content)} bytes")
        
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file received")
        
        try:
            image = read_file_as_image(file_content)
            print(f"Successfully read image. Shape: {image.shape}, dtype: {image.dtype}")
        except Exception as e:
            print(f"Error reading image: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=400, detail=f"Invalid image file: {str(e)}")
        
        # Ensure image has correct shape and type
        if len(image.shape) != 3:
            print(f"Invalid image shape: {image.shape}")
            raise HTTPException(status_code=400, detail=f"Image must be 3D (got shape {image.shape})")
        
        if image.shape[2] != 3:
            print(f"Invalid number of channels: {image.shape[2]}")
            raise HTTPException(status_code=400, detail=f"Image must be RGB (3 channels, got {image.shape[2]})")
        
        # Resize image if necessary
        if image.shape[:2] != (256, 256):
            print(f"Resizing image from {image.shape[:2]} to (256, 256)")
            try:
                image = tf.image.resize(image, (256, 256))
                print("Image resized successfully")
            except Exception as e:
                print(f"Error resizing image: {str(e)}")
                print(f"Error type: {type(e)}")
                print(f"Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=400, detail=f"Error resizing image: {str(e)}")
        
        # Normalize image to [0, 1] range
        try:
            image = tf.cast(image, tf.float32) / 255.0
            print(f"Image normalized. Value range: [{np.min(image)}, {np.max(image)}]")
        except Exception as e:
            print(f"Error normalizing image: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=400, detail=f"Error normalizing image: {str(e)}")
        
        # Add batch dimension
        img_batch = np.expand_dims(image, 0)
        print(f"Batch shape: {img_batch.shape}")
        
        # Make prediction
        print("Making prediction...")
        try:
            print("Model summary:")
            tensorflow_model.summary()
            print("Input shape expected by model:", tensorflow_model.input_shape)
            print("Output shape expected by model:", tensorflow_model.output_shape)
            print("Input batch shape:", img_batch.shape)
            print("Input batch dtype:", img_batch.dtype)
            print("Input batch value range:", [np.min(img_batch), np.max(img_batch)])
            
            predictions = tensorflow_model.predict(img_batch, verbose=0)
            print(f"Raw predictions: {predictions}")
        except Exception as e:
            print(f"Error during prediction: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Error during model prediction: {str(e)}")
        
        try:
            predicted_class_idx = np.argmax(predictions[0])
            predicted_class = CLASS_NAMES[predicted_class_idx]
            confidence = float(np.max(predictions[0]))
            
            print(f"Predicted class index: {predicted_class_idx}")
            print(f"Predicted class: {predicted_class}")
            print(f"Confidence: {confidence}")
            
            response_data = {
                'class': predicted_class,
                'confidence': confidence,
                'class_index': int(predicted_class_idx),
                'all_predictions': predictions[0].tolist()
            }
            print(f"Sending response: {response_data}")
            
            return JSONResponse(
                content=response_data,
                headers={"Access-Control-Allow-Origin": "http://localhost:5173"}
            )
        except Exception as e:
            print(f"Error processing prediction results: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Error processing prediction results: {str(e)}")
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"Unexpected error in predict_tensorflow: {str(e)}")
        print(f"Error type: {type(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

# YOLOv8 prediction endpoint
@app.post("/predict/yolo")
async def predict_yolo(file: UploadFile = File(...)):
    """
    Predict endpoint for YOLOv8 object detection.
    - Saves the annotated image.
    - Returns predictions and the URL of the annotated image.
    """
    try:
        # Read the uploaded image
        image = read_file_as_image(await file.read())

        # Run YOLOv8 model prediction
        results = yolo_model.predict(image)

        # Save annotated image with a specific format (PNG)
        annotated_img_path = os.path.join(OUTPUT_DIR, f"annotated_{file.filename}")
        annotated_img_path = annotated_img_path.split('.')[0] + ".png"  # Ensure PNG format
        image_array = results[0].plot()
        
        # Save the annotated image
        Image.fromarray(image_array).convert("RGB").save(annotated_img_path, format='PNG')

        # Extract predictions
        output = []
        if results[0].boxes is not None:  # Handle cases with detections
            for box in results[0].boxes.data:
                cls = int(box[5].item())  # Class index (assumes index 5 is class)
                conf = float(box[4].item())  # Confidence score (assumes index 4 is confidence)
                bbox = [float(coord) for coord in box[:4].tolist()]  # Bounding box coordinates (assumes first 4 are xyxy)

                label = CUSTOM_LABELS.get(cls, "Unknown")
            
                output.append({
                    "label": label,
                    "confidence": conf,
                    "bbox": bbox
                })

        return JSONResponse({
            "predictions": output,
            "annotated_image_url": f"http://localhost:8000/annotated_images/{os.path.basename(annotated_img_path)}"
        })
    except Exception as e:
        print(f"Error in predict_yolo: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/annotated_images/{image_path:path}")
async def serve_image(image_path: str):
    """
    Endpoint to serve annotated images.
    """
    full_path = os.path.join(OUTPUT_DIR, image_path)
    
    # Check if the image exists
    if os.path.exists(full_path):
        return FileResponse(full_path)
    
    return JSONResponse({"error": "Image not found"}, status_code=404)

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)

